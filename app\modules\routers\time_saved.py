from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from datetime import datetime
from typing import Optional
from app.modules.services.time_saved_service import TimeSavedService
from app.models.analytics.time_saved import TimeSaved

router = APIRouter()

class TimeSavedRequest(BaseModel):
    client_id: str
    clinician_id: str
    status: str  # note, save, or sign
    session_id: Optional[str] = None
    document_version_id: Optional[int] = None
    procedure_code: Optional[str] = None
    teams: Optional[str] = None
    teams_code: Optional[str] = None
    tool_used: Optional[bool] = None

def _serialize_time_saved(record: TimeSaved) -> dict:
    return {
        "id": record.id,
        "client_id": record.client_id,
        "clinician_id": record.clinician_id,
        "document_version_id": record.document_version_id,
        "session_id": record.session_id,
        "start_timestamp": record.start_timestamp,
        "end_timestamp": record.end_timestamp,
        "duration": record.duration,
        "procedure_code": record.procedure_code,
        "teams": record.teams,
        "teams_code": record.teams_code,
        "division": record.division,
        "division_code": record.division_code,
        "program": record.program,
        "program_code": record.program_code,
        "tool_used": record.tool_used,
        "status": record.status,
        "updated_at": record.updated_at
    }

@router.post("/time_saved")
async def save_time_saved(request: TimeSavedRequest):
    # Service now returns (record, already_exists_flag)
    record, already_exists = await TimeSavedService.record_time_saved(
        client_id=request.client_id,
        clinician_id=request.clinician_id,
        status=request.status,
        session_id=request.session_id or "",
        document_version_id=request.document_version_id or 0,
        procedure_code=request.procedure_code,
        teams=request.teams,
        teams_code=request.teams_code,
        tool_used=request.tool_used
    )
    outer_status = "already_exists" if already_exists else "success"
    return {"status": outer_status, "data": _serialize_time_saved(record)} 