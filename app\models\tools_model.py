from pydantic import BaseModel, Field, field_validator, model_validator
from typing import Any, Dict, Optional, List

class ToolParameters(BaseModel):
    
    notes: Optional[str] = None
    user_instruction: Optional[str] = None
    selected_issues: Optional[List[str]] = None
    action_ref_id: Optional[str] = None
    
    
    pronoun: Optional[str] = ""
    case_worker_name: Optional[str] = None
    procedure_code: Optional[str] = None
    needs: Optional[str] = None
    objectives: Optional[str] = None
    goals: Optional[str] = None
    interventions: Optional[str] = None
    mode_of_delivery: Optional[str] = None
    member_presentation: Optional[str] = ""
    response_to_interventions: Optional[str] = ""
    progress_towards_objectives: Optional[str] = ""
    member_quotes: Optional[str] = ""
    service_duration: Optional[str] = None
    member_response: Optional[str] = None
    member_present: Optional[str] = None

    model_config = {
        'error_messages': {
            'value_error.missing': 'Field is required',
            'type_error.none.not_allowed': 'Field cannot be null',
            'type_error.list': 'Must be a list',
            'type_error.str': 'Must be a string'
        }
    }

    def validate_for_action(self, action_type: str) -> None:
   
        if action_type == "generate":
            self._validate_generate()
        elif action_type == "cleanup":
            self._validate_cleanup()
        elif action_type == "finetune":
            self._validate_finetune()
        elif action_type == "regenerate":
            self._validate_regenerate()

    def _validate_generate(self) -> None:
     
        required_fields = ['response_to_interventions', 'progress_towards_objectives']
        missing_fields = [field for field in required_fields if not getattr(self, field)]
        if missing_fields:
            raise ValueError(f"Missing required fields for generate action: {', '.join(missing_fields)}")

    def _validate_cleanup(self) -> None:
    
        if not self.notes or not self.notes.strip():
            raise ValueError("Notes field is required for cleanup action")

    def _validate_finetune(self) -> None:
    
        if not self.notes or not self.notes.strip():
            raise ValueError("Notes field is required for finetune action")
        if  self.user_instruction is None:
            raise ValueError("User instruction is required for finetune action")


    def _validate_regenerate(self) -> None:
        
        if not self.notes or not self.notes.strip():
            raise ValueError("Notes field is required for regenerate action")
        if self.user_instruction is None:
            raise ValueError("User instruction is required for regenerate action")
        if self.selected_issues is None:
            raise ValueError("At least one issue must be selected for regenerate action")
        if self.action_ref_id and not self.action_ref_id.strip():
            raise ValueError("Action reference ID cannot be empty if provided")

class ToolActionRequest(BaseModel):
    action_type: str = Field(..., description="Type of action to perform")
    client_id: str = Field(..., description="Client identifier")
    documentversion_id: int = Field(..., description="Document version identifier")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Action-specific parameters")

    @field_validator('action_type')
    @classmethod
    def validate_action_type(cls, v):
        valid_types = ["generate", "cleanup", "finetune", "regenerate", "undo", "insert&edit"]
        if v.lower() not in valid_types:
            raise ValueError(f"Invalid action_type: {v}. Must be one of: {', '.join(valid_types)}")
        return v.lower()

    @field_validator('client_id')
    @classmethod
    def validate_client_id(cls, v):
        if not v.strip():
            raise ValueError("Client ID cannot be empty")
        return v

    @field_validator('documentversion_id')
    @classmethod
    def validate_document_id(cls, v):
        if v <= 0:
            raise ValueError("Document version ID must be positive")
        return v

    @model_validator(mode='after')
    def validate_parameters(self):
        if not self.parameters:
            raise ValueError("Parameters are required")

        try:
            tool_params = ToolParameters(**self.parameters)
         
            if self.action_type not in ["undo", "insert&edit"]:
                tool_params.validate_for_action(self.action_type)
        except Exception as e:
            raise ValueError(f"Invalid parameters for {self.action_type}: {str(e)}")

        return self