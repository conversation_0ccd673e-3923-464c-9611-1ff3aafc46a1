from sqlalchemy import Column, Integer, String, Text
from app.connections.summary_database import SummaryBase as Base

class Feedback(Base):
    __tablename__ = "feedback"

    feedback_id = Column(Integer, primary_key=True, index=True)
    feedback_provided = Column(Integer)
    feedback_category = Column(String, nullable=True)
    feedback_notes = Column(Text, nullable=True)
    action_type = Column(String)
    action_id = Column(Integer)
    session_id = Column(String)
    action_ref_id = Column(String) 