from pydantic import BaseModel, Field
from datetime import datetime
import time

class Session(BaseModel):
    id: str
    document_id: int
    start_time: datetime = Field(default_factory=datetime.now)
    last_activity: datetime = Field(default_factory=datetime.now)
    is_active: bool = True
    @classmethod
    def create_session_id(cls, document_id: int) -> str:
      
        timestamp = int(time.time() * 1000)
        return f"{document_id}{timestamp}"
