import logging
import os
import datetime
import json
import functools
from decouple import config
import requests
from app.core.config import ANALYTICS_API_BASE_URL, ANALYTICS_API_KEY, ANALYTICS_FAILURE_LOGS_ENDPOINT, SEVERITY_CRITICAL, STATUS_UNRESOLVED, FAILURE_LOG_TYPE, FAILURE_LOG_REPOSITORY


_logger = None

def get_logger():
  
    global _logger
    if _logger is None:
        log_dir = config('LOG_DIR', default='log')
        os.makedirs(log_dir, exist_ok=True)

        current_timestamp = datetime.datetime.now().strftime('%Y-%m-%d')
        log_file = os.path.join(log_dir, f'{current_timestamp}.log')

        log_level = config('LOG_LEVEL', default='INFO').upper()

        _logger = logging.getLogger('app')
        _logger.setLevel(getattr(logging, log_level, logging.INFO))

        if not _logger.hasHandlers():
            console_handler = logging.StreamHandler()
            file_handler = logging.FileHandler(log_file)
            console_handler.setLevel(logging.DEBUG)
            file_handler.setLevel(getattr(logging, log_level, logging.INFO))
            
                
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            
            console_handler.setFormatter(formatter)
            file_handler.setFormatter(formatter)

            _logger.addHandler(console_handler)
            _logger.addHandler(file_handler)

            failure_handler = FailureLogHandler()
            failure_handler.setFormatter(formatter)
            _logger.addHandler(failure_handler)

    return _logger

def log_api_transaction(action_type, include_payload=True, include_response=True, mask_fields=None):
 
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            logger = get_logger()
            
                
            document_id = kwargs.get('documentversion_id', None)
            session_id = kwargs.get('session_id', None)
            action_ref_id = kwargs.get('action_ref_id', None)
            
            
            identifiers = []
            if document_id: identifiers.append(f"document_id={document_id}")
            if session_id: identifiers.append(f"session_id={session_id}")
            if action_ref_id: identifiers.append(f"action_ref_id={action_ref_id}")
            identifier_str = " | ".join(identifiers) if identifiers else "no_identifier"
            
            
            logger.info(f"{action_type} action started | {identifier_str}")
            
            
            if include_payload:
                masked_kwargs = kwargs.copy()
                if mask_fields:
                    for field in mask_fields:
                        if field in masked_kwargs:
                            masked_kwargs[field] = "********"
                payload_str = json.dumps(masked_kwargs, default=str)
                logger.info(f"{action_type} payload | {identifier_str} | {payload_str}")
            
            
            start_time = datetime.datetime.now()
            try:
                response = await func(*args, **kwargs)
                
                
                if include_response:
                    
                    logger.info(f"{action_type} response received | {identifier_str}")
                
                return response
            except Exception as e:
                logger.error(f"{action_type} action failed | {identifier_str} | error={str(e)}")
                
                error_response = {
                    "status": "error",
                    "message": f"Error in {action_type}: {str(e)}",
                    "error_type": type(e).__name__,
                    "action_type": action_type,
                    "document_id": document_id
                }
                return error_response
                
        return wrapper
    return decorator

class FailureLogHandler(logging.Handler):
    def __init__(self):
        super().__init__(level=logging.ERROR)
        # Build URL preserving the trailing slash on the endpoint to avoid redirect
        self.url = ANALYTICS_API_BASE_URL.rstrip('/') + ANALYTICS_FAILURE_LOGS_ENDPOINT
        self.headers = {"Content-Type": "application/json", "X-API-Key": ANALYTICS_API_KEY}

    def emit(self, record):
        try:
            # Build failure log payload using configured constants
            exc_type, exc_value, _ = record.exc_info or (None, None, None)
            payload = {
                "errorType": exc_type.__name__ if exc_type else record.levelname,
                "errorMessage": str(exc_value) if exc_value else self.format(record),
                "failureContext": f"{record.module}.{record.funcName}",
                "severity": SEVERITY_CRITICAL,
                "status": STATUS_UNRESOLVED,
                "type": FAILURE_LOG_TYPE,
                "repository": FAILURE_LOG_REPOSITORY
            }
            requests.post(self.url, json=payload, headers=self.headers, timeout=5)
        except Exception:
            pass

logger = get_logger() 
