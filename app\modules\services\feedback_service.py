from fastapi import HTT<PERSON>Exception, Request
from app.connections.analytics_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core.logger import logger
from app.modules.services.session_service import SessionService
from app.modules.services.summary_db_service import SummaryDBService
from app.utils.crypto_engine_decorator import secure_payload
from typing import Optional

session_service = SessionService()

@secure_payload
async def process_feedback_submission(feedback_provided: int, action_ref_id: str,
                                    feedback_category: Optional[str] = None, feedback_notes: Optional[str] = None):
    """
    Process encrypted feedback submission

    Args:
        feedback_provided: Feedback value (0=no feedback, 1=thumbs-down, 2=thumbs-up)
        action_ref_id: Reference ID for the action being given feedback on
        feedback_category: Optional category of feedback
        feedback_notes: Optional feedback notes

    Returns:
        dict: Result from analytics API
    """
    logger.info(f"Processing feedback submission | action_ref_id={action_ref_id} | feedback_provided={feedback_provided}")

    try:
        # Validate the feedback_provided value (should be 0, 1, or 2)
        if feedback_provided not in [0, 1, 2]:
            logger.error(f"Invalid feedback_provided value: {feedback_provided}")
            raise HTTPException(
                status_code=400, 
                detail="feedback_provided must be 0 (no feedback), 1 (thumbs-down), or 2 (thumbs-up)"
            )

        # Prepare feedback data for analytics
        feedback_data = {
            "feedback_provided": feedback_provided,
            "feedback_category": feedback_category,
            "feedback_notes": feedback_notes,
            "action_ref_id": action_ref_id
        }

        # Track feedback in analytics system
        async with AnalyticsHandler() as analytics:
            api_result = await analytics.track_feedback(feedback_data)

        # Store feedback in summary database
        await SummaryDBService.store_feedback(
            feedback_provided=feedback_provided,
            action_ref_id=action_ref_id,
            feedback_category=feedback_category,
            feedback_notes=feedback_notes
        )

        logger.info(f"Feedback submission completed successfully | action_ref_id={action_ref_id}")
        return api_result

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error processing feedback submission | action_ref_id={action_ref_id} | error={str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing feedback submission: {str(e)}")
