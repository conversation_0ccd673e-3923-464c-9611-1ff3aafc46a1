#!/usr/bin/env python3
"""
Simple test server to check if the feedback endpoint works
"""
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from app.modules.routers.feedback import router as feedback_router
from app.modules.routers.public_key import router as key_router
from app.core.auth import verify_api_key
from fastapi import Depends

app = FastAPI(title="Test Server")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers with API key dependency
app.include_router(feedback_router, prefix="/api/v1", dependencies=[Depends(verify_api_key)])
app.include_router(key_router, prefix="/api/v1", dependencies=[Depends(verify_api_key)])

@app.get("/")
async def root():
    return {"message": "Test server is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)
