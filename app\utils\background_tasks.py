import asyncio
from typing import Any, Coroutine
from app.core.logger import logger


class BackgroundTaskManager:
    
    
    @staticmethod
    def create_task(coro: Coroutine) -> asyncio.Task:
        
        task = asyncio.create_task(BackgroundTaskManager._task_wrapper(coro))
        return task
    
    @staticmethod
    async def _task_wrapper(coro: Coroutine) -> Any:
        
        try:
            return await coro
        except Exception as e:
            logger.error(f"Background task failed: {str(e)}", exc_info=True)
            return None