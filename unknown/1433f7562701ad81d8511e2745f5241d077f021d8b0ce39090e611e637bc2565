from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request
from app.utils import crypto_utils
from functools import wraps
import base64
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives import hashes
from app.core.logger import logger


def secure_payload(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        request: Request = kwargs.get('request')
        if request is None:
            logger.error("Missing 'request' in kwargs.")
            raise HTTPException(status_code=400, detail="Request object required for secure payload.")

        try:
            body = await request.json()
            logger.info("Decrypting incoming payload.")
            decrypted = crypto_utils.decrypt_payload(
                encrypted_data=body['encrypted_data'],
                encrypted_key=body['encrypted_key'],
                nonce=body['nonce']
            )

            logger.info("Calling secured route with decrypted data.")
            result = await func(*args, **decrypted)

            logger.info("Reconstructing AES key from encrypted_key.")
            aes_key = crypto_utils.private_key.decrypt(
                base64.b64decode(body['encrypted_key']),
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )

            logger.info("Encrypting response data.")
            encrypted_data, response_nonce = crypto_utils.encrypt_response(result, aes_key)

            logger.info("Returning encrypted response.")
            return {"encrypted_data": encrypted_data, "nonce": response_nonce}

        except Exception as e:
            logger.error(f"Encryption/decryption process failed: {e}")
            raise HTTPException(status_code=500, detail=f"Encryption/decryption failed: {str(e)}")

    return wrapper