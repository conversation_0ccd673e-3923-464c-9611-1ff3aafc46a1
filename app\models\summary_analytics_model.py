from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class SummaryAnalyticsRequest(BaseModel):
    clinician_id: str
    document_id: int
    clinician_type: Optional[str] = None
    procedure_code: Optional[str] = None
    auto_completion_used: Optional[bool] = False
    summary_finalized_timestamp: Optional[datetime] = None
    summary_finalized: Optional[bool] = False
    start_timestamp: Optional[datetime] = None
    end_timestamp: Optional[datetime] = None
    time_consumed_secs: Optional[int] = None
    client_id:Optional[str] = None
    teams: Optional[str] = None
    teams_code:Optional[str] = None
    division:Optional[str] = None
    division_code: Optional[str] = None
    program:Optional[str] = None
    program_code:Optional[str] = None
