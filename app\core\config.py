from decouple import config

API_KEY = config('API_KEY')
API_TITLE = config('API_TITLE', default='alpha-api-toolinterface')
API_VERSION = config('API_VERSION', default='0.3.1')
API_BUILD_NUMBER = config('API_BUILD_NUMBER', default='0012')
API_BUILD_DATE = config('API_BUILD_DATE', default='16/06/2025')
ENVIRONMENT = config('ENVIRONMENT', default='ALPHA')

# Summary Database (no defaults)
SUMMARY_DB_SERVER = config('SUMMARY_DB_SERVER')
SUMMARY_DB_DATABASE = config('SUMMARY_DB_DATABASE')
SUMMARY_DB_USERNAME = config('SUMMARY_DB_USERNAME')
SUMMARY_DB_PASSWORD = config('SUMMARY_DB_PASSWORD')
SUMMARY_DB_DRIVER = config('SUMMARY_DB_DRIVER')

# Analytics Database
ANALYTICS_DB_SERVER = config('ANALYTICS_DB_SERVER')
ANALYTICS_DB_PORT = config('ANALYTICS_DB_PORT')
ANALYTICS_DB_NAME = config('ANALYTICS_DB_NAME')
ANALYTICS_DB_USERNAME = config('ANALYTICS_DB_USERNAME')
ANALYTICS_DB_PASSWORD = config('ANALYTICS_DB_PASSWORD')
ANALYTICS_DB_DRIVER = config('ANALYTICS_DB_DRIVER')

# LLM Configuration (no defaults for endpoints)
LLM_API_BASE_URL = config('LLM_API_BASE_URL')
LLM_API_GENERATE_ENDPOINT = config('LLM_API_GENERATE_ENDPOINT', default='/generate_notes')
LLM_API_CLEANUP_ENDPOINT = config('LLM_API_CLEANUP_ENDPOINT', default='/cleanup_notes')
LLM_API_FINETUNE_ENDPOINT = config('LLM_API_FINETUNE_ENDPOINT', default='/finetune_notes')


# Analytics Configuration (no defaults for endpoints)
ANALYTICS_API_BASE_URL = config('ANALYTICS_API_BASE_URL')
ANALYTICS_API_KEY = config('ANALYTICS_API_KEY')
ANALYTICS_ACTION_ENDPOINT = config('ANALYTICS_ACTION_ENDPOINT', default='/api/actions')
ANALYTICS_FEEDBACK_ENDPOINT = config('ANALYTICS_FEEDBACK_ENDPOINT', default='/api/feedback')
ANALYTICS_SUMMARY_ENDPOINT = config('ANALYTICS_SUMMARY_ENDPOINT', default='/api/summary-analytics')
ANALYTICS_TOKEN_COST_ENDPOINT = config('ANALYTICS_TOKEN_COST_ENDPOINT', default='/api/v1/metrics/store-llm-token-costs')
ANALYTICS_LLM_TRAFFIC_ENDPOINT = config('ANALYTICS_LLM_TRAFFIC_ENDPOINT', default='/api/v1/llm-traffic')
ANALYTICS_FAILURE_LOGS_ENDPOINT = config('ANALYTICS_FAILURE_LOGS_ENDPOINT', default='/api/v1/failure-logs')
# CORS
CORS_ORIGINS = config('CORS_ORIGINS', default='*').split(',')
CORS_CREDENTIALS = config('CORS_CREDENTIALS', default='true').lower() == 'true'
CORS_METHODS = config('CORS_METHODS', default='*').split(',')
CORS_HEADERS = config('CORS_HEADERS', default='*').split(',')

# Session
SESSION_MAX_AGE_MINUTES = config('SESSION_MAX_AGE_MINUTES', default=30, cast=int)

# API
API_PREFIX = config('API_PREFIX', default='/api/v1')

# Docs
DOCS_OAUTH2_REDIRECT_URL = config('DOCS_OAUTH2_REDIRECT_URL', default='/docs/oauth2-redirect')
SWAGGER_JS_URL = config('SWAGGER_JS_URL', default='https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js')
SWAGGER_CSS_URL = config('SWAGGER_CSS_URL', default='https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css')
REDOC_JS_URL = config('REDOC_JS_URL', default='https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js')

# OpenAPI and Docs endpoints
OPENAPI_URL = config('OPENAPI_URL', default='/openapi.json?api_key=' + API_KEY)
DOCS_URL = config('DOCS_URL', default='/docs?api_key=' + API_KEY)
REDOC_URL = config('REDOC_URL', default='/redoc?api_key=' + API_KEY)

# Timeouts & Currency
LLM_REQUEST_TIMEOUT = config('LLM_REQUEST_TIMEOUT', default=30, cast=int)
ANALYTICS_REQUEST_TIMEOUT = config('ANALYTICS_REQUEST_TIMEOUT', default=15, cast=int)
CURRENCY_CODE = config('CURRENCY_CODE', default='USD')

# Failure logs defaults
SEVERITY_CRITICAL = config('SEVERITY_CRITICAL', default='Critical')
STATUS_UNRESOLVED = config('STATUS_UNRESOLVED', default='Un-Resolved')
FAILURE_LOG_TYPE = config('FAILURE_LOG_TYPE', default='System Logs')
FAILURE_LOG_REPOSITORY = config('FAILURE_LOG_REPOSITORY', default='API-Tool-Interface')
LLM_FAILURE_LOG_TYPE = config('LLM_FAILURE_LOG_TYPE', default='LLM Logs')

# Table names
TIME_SAVED_TABLE_NAME = config('TIME_SAVED_TABLE_NAME', default='time_saved')

VAULT_URL = config("AZURE_KEY_VAULT_URL")
CLIENT_ID = config("AZURE_CLIENT_ID")
CLIENT_SECRET = config("AZURE_CLIENT_SECRET")
TENANT_ID = config("AZURE_TENANT_ID")

AZURE_KV_RSA_PUBLIC_KEY_NAME= config('AZURE_KV_RSA_PUBLIC_KEY_NAME', default='rsa-public-key')
AZURE_KV_RSA_PRIVATE_KEY_NAME= config('AZURE_KV_RSA_PUBLIC_KEY_NAME', default='rsa-private-key')