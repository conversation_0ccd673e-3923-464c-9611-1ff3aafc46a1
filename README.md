# Document Info API

A FastAPI application that provides an endpoint to retrieve document information from MSSQL database using a stored procedure.

## Features

- Secure API endpoint with API key authentication
- MSSQL database integration
- Stored procedure execution
- Error handling and proper response formatting

## Prerequisites

- Python 3.8+
- MSSQL Server
- ODBC Driver for SQL Server

## Setup

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Configure the environment variables in `.env` file:
```
DB_SERVER=your_server_name
DB_DATABASE=your_database_name
DB_USERNAME=your_username
DB_PASSWORD=your_password
API_KEY=your_secure_api_key_here
```

3. Run the application:
```bash
uvicorn main:app --reload
```

## API Usage

### Get Document Info

**Endpoint:** `/get-document-info`

**Method:** GET

**Headers:**
- X-API-Key: Your API key

**Query Parameters:**
- client_id (required): The client ID to query
- staff_id (optional): The staff ID to filter results

**Example Request:**
```bash
curl -X GET "http://localhost:8000/get-document-info?client_id=123&staff_id=456" \
     -H "X-API-Key: your_api_key_here"
```

**Example Response:**
```json
{
    "data": [...],
    "status": "success",
    "message": "Data retrieved successfully"
}
```

## Security

- API Key authentication is required for all endpoints
- Database credentials are stored in environment variables
- SQL injection prevention through parameterized queries
- Proper error handling and logging

## Error Handling

The API returns appropriate HTTP status codes:
- 200: Successful request
- 403: Invalid API key
- 500: Server error (database connection issues, etc.) 