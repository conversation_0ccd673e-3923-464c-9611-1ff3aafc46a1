import logging
from urllib.parse import quote_plus
from typing import As<PERSON><PERSON>enerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import NullPool
from app.core.config import ANALY<PERSON>CS_DB_SERVER, ANALYTICS_DB_PORT, ANALYTICS_DB_NAME, ANALYTICS_DB_USERNAME, ANALYTICS_DB_PASSWORD, ANALYTICS_DB_DRIVER
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

logger.info("Analytics async engine configured.")

AnalyticsBase = declarative_base()

def generate_analytics_database_url() -> str:
    server = ANALYTICS_DB_SERVER
    port = ANALYTICS_DB_PORT or '1433'
    quoted_password = quote_plus(ANALYTICS_DB_PASSWORD)
    quoted_driver = quote_plus(ANALYTICS_DB_DRIVER)
    return f"mssql+aioodbc://{ANALYTICS_DB_USERNAME}:{quoted_password}@{server}:{port}/{ANALYTICS_DB_NAME}?driver={quoted_driver}&TrustServerCertificate=yes&Encrypt=yes&ConnectionTimeout=30&LoginTimeout=30"

ANALYTICS_DATABASE_URL = generate_analytics_database_url()

analytics_engine = create_async_engine(
    ANALYTICS_DATABASE_URL,
    echo=False,
    poolclass=NullPool,
    connect_args={
        "timeout": 30,
        "autocommit": False
    }
)

analytics_async_session = sessionmaker(
    analytics_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

@asynccontextmanager
async def get_analytics_db() -> AsyncGenerator[AsyncSession, None]:
    session = analytics_async_session()
    logger.info("Opening analytics DB session")
    try:
        yield session
        await session.commit()
        logger.info("Committed analytics DB session")
    except Exception as e:
        await session.rollback()
        logger.exception("Exception occurred in get_analytics_db: %s", e)
        raise
    finally:
        await session.close()
        logger.info("Closed analytics DB session")

async def init_analytics_db():
    try:
        async with analytics_engine.begin() as conn:
            await conn.run_sync(AnalyticsBase.metadata.create_all)
            logger.info("Analytics database tables created successfully")
    except Exception as e:
        logger.exception("Exception occurred during analytics database initialization: %s", e)
        raise

async def close_analytics_db_connection():
    try:
        await analytics_engine.dispose()
    except Exception as e:
        logger.exception("Exception occurred while closing the analytics database connection: %s", e)
        raise 