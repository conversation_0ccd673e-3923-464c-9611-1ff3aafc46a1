import logging
from datetime import datetime
from typing import Optional
from sqlalchemy.future import select
from sqlalchemy import update
from app.connections.summary_database import get_summary_db
from app.models.summary.action import Action
from app.models.summary.feedback import Feedback
from app.models.summary.summary_analytics import SummaryAnalytics

logger = logging.getLogger(__name__)

class SummaryDBService:
    """Service for interacting with the summary database."""
    
    @staticmethod
    async def store_action(
        session_id: str,
        action_type: str,
        prompt_tokens: int,
        completion_tokens: int,
        total_tokens: int,
        request_timestamp: str,
        response_timestamp: str,
        total_response_time_ms: int,
        action_ref_id: str,
        summary: Optional[str] = None
    ) -> Action:
        """Store action data in the summary database."""
        try:
            async with get_summary_db() as db:
                req_timestamp = request_timestamp
                res_timestamp = response_timestamp
                if isinstance(request_timestamp, str):
                    req_timestamp = datetime.fromisoformat(request_timestamp.replace('Z', '+00:00'))
                if isinstance(response_timestamp, str):
                    res_timestamp = datetime.fromisoformat(response_timestamp.replace('Z', '+00:00'))
                action = Action(
                    session_id=session_id,
                    action_type=action_type,
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    total_tokens=total_tokens,
                    request_timestamp=req_timestamp,
                    response_timestamp=res_timestamp,
                    total_response_time_ms=total_response_time_ms,
                    action_ref_id=action_ref_id,
                    summary=summary
                )
                db.add(action)
                await db.commit()
                await db.refresh(action)
                logger.info(f"Stored action in summary database: {action_ref_id}")
                return action
        except Exception as e:
            logger.error(f"Error storing action in summary database: {str(e)}")
            raise
    
    @staticmethod
    async def store_feedback(
        feedback_provided: int,
        action_ref_id: str,
        feedback_category: Optional[str] = None,
        feedback_notes: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Feedback:
        """Store feedback data in the summary database."""
        try:
            async with get_summary_db() as db:
                action_id = None
                action_type = None
                if not session_id:
                    action_query = select(Action).where(Action.action_ref_id == action_ref_id)
                    action_result = await db.execute(action_query)
                    action = action_result.scalar_one_or_none()
                    if action:
                        session_id = action.session_id
                        action_id = action.action_id
                        action_type = action.action_type
                feedback_query = select(Feedback).where(Feedback.action_ref_id == action_ref_id)
                feedback_result = await db.execute(feedback_query)
                existing_feedback = feedback_result.scalar_one_or_none()
                if existing_feedback:
                    stmt = (
                        update(Feedback)
                        .where(Feedback.action_ref_id == action_ref_id)
                        .values(
                            feedback_provided=feedback_provided,
                            feedback_category=feedback_category,
                            feedback_notes=feedback_notes,
                            session_id=session_id,
                            action_id=action_id,
                            action_type=action_type
                        )
                    )
                    await db.execute(stmt)
                    await db.commit()
                    feedback_result = await db.execute(feedback_query)
                    feedback = feedback_result.scalar_one()
                    logger.info(f"Updated existing feedback in summary database for action: {action_ref_id}")
                else:
                    feedback = Feedback(
                        feedback_provided=feedback_provided,
                        feedback_category=feedback_category,
                        feedback_notes=feedback_notes,
                        action_ref_id=action_ref_id,
                        session_id=session_id,
                        action_id=action_id,
                        action_type=action_type
                    )
                    db.add(feedback)
                    await db.commit()
                    await db.refresh(feedback)
                    logger.info(f"Stored new feedback in summary database for action: {action_ref_id}")
                return feedback
        except Exception as e:
            logger.error(f"Error storing feedback in summary database: {str(e)}")
            raise
    
    @staticmethod
    async def store_summary_analytics(
        user_id: str,
        session_id: str,
        clinician_type: Optional[str] = None,
        procedure_code: Optional[str] = None,
        auto_completion_used: Optional[bool] = False,
        summary_finalized: Optional[bool] = False,
        start_timestamp: Optional[datetime] = None,
        end_timestamp: Optional[datetime] = None,
        summary_finalized_timestamp: Optional[datetime] = None,
        time_consumed_secs: Optional[int] = None,
        summary: Optional[str] = None
    ) -> SummaryAnalytics:
        """Store summary analytics data in the summary database."""
        try:
            async with get_summary_db() as db:
                query = select(SummaryAnalytics).where(SummaryAnalytics.session_id == session_id)
                result = await db.execute(query)
                existing_analytics = result.scalar_one_or_none()
                if existing_analytics:
                    stmt = (
                        update(SummaryAnalytics)
                        .where(SummaryAnalytics.session_id == session_id)
                        .values(
                            auto_completion_used=auto_completion_used if auto_completion_used is not None else existing_analytics.auto_completion_used,
                            summary_finalized=summary_finalized if summary_finalized is not None else existing_analytics.summary_finalized,
                            end_timestamp=end_timestamp or existing_analytics.end_timestamp,
                            summary_finalized_timestamp=summary_finalized_timestamp or existing_analytics.summary_finalized_timestamp,
                            time_consumed_secs=time_consumed_secs or existing_analytics.time_consumed_secs,
                            summary=summary if summary is not None else existing_analytics.summary
                        )
                    )
                    await db.execute(stmt)
                    await db.commit()
                    result = await db.execute(query)
                    analytics = result.scalar_one()
                    logger.info(f"Updated summary analytics in database for session: {session_id}")
                else:
                    analytics = SummaryAnalytics(
                        user_id=user_id,
                        session_id=session_id,
                        clinician_type=clinician_type,
                        procedure_code=procedure_code,
                        auto_completion_used=auto_completion_used,
                        summary_finalized=summary_finalized,
                        start_timestamp=start_timestamp or datetime.utcnow(),
                        end_timestamp=end_timestamp,
                        summary_finalized_timestamp=summary_finalized_timestamp,
                        time_consumed_secs=time_consumed_secs,
                        summary=summary
                    )
                    db.add(analytics)
                    await db.commit()
                    await db.refresh(analytics)
                    logger.info(f"Stored new summary analytics in database for session: {session_id}")
                return analytics
        except Exception as e:
            logger.error(f"Error storing summary analytics in database: {str(e)}")
            raise 