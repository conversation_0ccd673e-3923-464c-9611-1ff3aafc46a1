from app.models.session_model import Session
from typing import Optional
from datetime import datetime, timedelta
from app.core.logger import logger
from app.core.config import SESSION_MAX_AGE_MINUTES

class SessionService:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SessionService, cls).__new__(cls)
            cls._instance.sessions = {}
        return cls._instance
        
    def create_session(self, document_id: int) -> Session:

        session_id = Session.create_session_id(document_id)
        session = Session(
            id=session_id,
            document_id=document_id
        )
        self.sessions[session_id] = session
        logger.info(f"Created new session | session_id={session_id} | document_id={document_id}")
        return session
    
    def get_session(self, session_id: str) -> Optional[Session]:
      
        return self.sessions.get(session_id)
    
    def get_session_by_document_id(self, document_id: int) -> Optional[Session]:
        
        for session in self.sessions.values():
            if session.document_id == document_id and session.is_active:
                session.last_activity = datetime.now()
                logger.info(f"Found existing session | session_id={session.id} | document_id={document_id}")
                return session
        logger.info(f"No active session found | document_id={document_id}")
        return None
    
    def get_or_create_session(self, document_id: int) -> Session:
      
        existing_session = self.get_session_by_document_id(document_id)
        if existing_session:
            existing_session.last_activity = datetime.now()
            logger.info(f"Reusing existing session | session_id={existing_session.id} | document_id={document_id}")
            return existing_session
        
        return self.create_session(document_id)
    
    def update_session_activity(self, session_id: str) -> Optional[Session]:
    
        session = self.get_session(session_id)
        if session:
            session.last_activity = datetime.now()
            logger.info(f"Updated session activity | session_id={session_id}")
        return session
    
    def close_session(self, session_id: str) -> bool:
     
        session = self.get_session(session_id)
        if session:
            session.is_active = False
            logger.info(f"Closed session | session_id={session_id}")
            return True
        return False
    
    def cleanup_inactive_sessions(self) -> int:
 
        now = datetime.now()
        cutoff_time = now - timedelta(minutes=SESSION_MAX_AGE_MINUTES)
        
        to_remove = []
        for session_id, session in self.sessions.items():
            if session.last_activity < cutoff_time:
                to_remove.append(session_id)
        
        for session_id in to_remove:
            del self.sessions[session_id]
            
        if to_remove:
            logger.info(f"Cleaned up inactive sessions | count={len(to_remove)} | session_ids={to_remove}")
            
        return len(to_remove)

