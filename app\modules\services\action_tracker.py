import time
import datetime
from app.connections.analytics_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.schemas.action import Action
from app.core.config import ANALYTICS_ACTION_ENDPOINT, ANALYTICS_API_KEY
from app.core.logger import logger
from app.modules.services.summary_db_service import SummaryDBService

class ActionTracker:
    
    
    def __init__(self):
        self.tracking_url = ANALYTICS_ACTION_ENDPOINT
        self.api_key = ANALYTICS_API_KEY
        
    
    async def track_action(self, 
                          session_id: int, 
                          action_type: str, 
                          start_time: float,
                          prompt_data: dict,
                          response_data: dict = None,
                          action_ref_id: str = None):
        
        end_time = time.time()
        request_timestamp = datetime.datetime.fromtimestamp(
            start_time, tz=datetime.timezone.utc).isoformat()
        response_timestamp = datetime.datetime.now(
            datetime.timezone.utc).isoformat()
        total_response_time_ms = int((end_time - start_time) * 1000)
        
        prompt_tokens = len(str(prompt_data)) // 4
        completion_tokens = len(str(response_data)) // 4 if response_data else 0
        total_tokens = prompt_tokens + completion_tokens
        
        action = Action(
            session_id=session_id, 
            action_type=action_type,
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens,
            request_timestamp=request_timestamp,
            response_timestamp=response_timestamp,
            total_response_time_ms=total_response_time_ms,
            action_ref_id=action_ref_id
        )
        
        try:
            # Store in analytics API
            async with AnalyticsHandler() as analytics:
                logger.info(f"Tracking action | action_type={action_type} | session_id={session_id} | action_ref_id={action_ref_id}")
                api_result = await analytics.track_action(action.dict())
            # Store in summary database
            summary = None
            if response_data and isinstance(response_data, dict) and "content" in response_data:
                summary = response_data["content"]
            await SummaryDBService.store_action(
                session_id=str(session_id),
                action_type=action_type,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                request_timestamp=request_timestamp,
                response_timestamp=response_timestamp,
                total_response_time_ms=total_response_time_ms,
                action_ref_id=action_ref_id,
                summary=summary
            )
            return api_result
        except Exception as e:
            logger.warning(f"Failed to track action: {str(e)}")
            return False
