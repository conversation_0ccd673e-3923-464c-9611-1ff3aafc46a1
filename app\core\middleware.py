from fastapi.openapi.utils import get_openapi
from app.core.config import API_TITLE, API_VERSION, API_BUILD_NUMBER, API_BUILD_DATE
from app.core.logger import logger

# Custom OpenAPI schema generator for FastAPI
def get_custom_openapi(app):
    def custom_openapi():
        if app.openapi_schema:
            return app.openapi_schema
            
        openapi_schema = get_openapi(
            title=API_TITLE,
            version=API_VERSION,
            description=f"Build Number: {API_BUILD_NUMBER}, Build Date: {API_BUILD_DATE}",
            routes=app.routes,
        )
        
        app.openapi_schema = openapi_schema
        logger.info("Custom OpenAPI schema generated")
        return app.openapi_schema
        
    return custom_openapi 