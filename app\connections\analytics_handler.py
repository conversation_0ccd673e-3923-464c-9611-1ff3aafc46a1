import aiohttp
from typing import Dict, Any, Optional
from app.core.config import ANALYTICS_API_BASE_URL, ANALYTICS_API_KEY, ANALYTICS_ACTION_ENDPOINT, ANALYTICS_FEEDBACK_ENDPOINT, ANALY<PERSON>CS_SUMMARY_ENDPOINT, ANALY<PERSON>CS_TOKEN_COST_ENDPOINT, ANALYTICS_REQUEST_TIMEOUT, ANALYTICS_LLM_TRAFFIC_ENDPOINT
from app.core.logger import logger
import time

class AnalyticsHandler:
    
    
    def __init__(self):
        self.action_tracking_url = ANALYTICS_API_BASE_URL + ANALYTICS_ACTION_ENDPOINT
        self.feedback_url = ANALYTICS_API_BASE_URL + ANALYTICS_FEEDBACK_ENDPOINT
        self.summary_analytics_url = ANALYTICS_API_BASE_URL + ANALYTICS_SUMMARY_ENDPOINT
        self.token_cost_url = ANALYTICS_API_BASE_URL + ANALYTICS_TOKEN_COST_ENDPOINT
        self.llm_traffic_url = ANALYTICS_API_BASE_URL + ANALYTICS_LLM_TRAFFIC_ENDPOINT
        self.api_key = ANALYTICS_API_KEY
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def track_action(self, action_data: Dict[str, Any]) -> bool:
        
        if not self.session:
            self.session = aiohttp.ClientSession()
            
        try:
            async with self.session.post(
                url=self.action_tracking_url,
                json=action_data,
                headers={
                    "Content-Type": "application/json",
                    "X-API-Key": self.api_key
                },
                timeout=ANALYTICS_REQUEST_TIMEOUT
            ) as response:
                response.raise_for_status()
                logger.info(f"Action tracked successfully | action_type={action_data.get('action_type')} | session_id={action_data.get('session_id')}")
                return True
                
        except Exception as e:
            logger.warning(f"Failed to track action | action_type={action_data.get('action_type')} | session_id={action_data.get('session_id')} | error={str(e)}")
            return False
            
    async def track_feedback(self, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        
        if not self.session:
            self.session = aiohttp.ClientSession()
            
        try:
            async with self.session.post(
                url=self.feedback_url,
                json=feedback_data,
                headers={
                    "Content-Type": "application/json",
                    "X-API-Key": self.api_key
                },
                timeout=ANALYTICS_REQUEST_TIMEOUT
            ) as response:
                response.raise_for_status()
                logger.info(f"Feedback tracked successfully | action_ref_id={feedback_data.get('action_ref_id')}")
                return await response.json()
                
        except Exception as e:
            logger.warning(f"Failed to track feedback | action_ref_id={feedback_data.get('action_ref_id')} | error={str(e)}")
            return {"status": "error", "message": str(e)}
        
    async def track_summary_analytics(self, summary_data: Dict[str, Any]) -> Dict[str, Any]:
    
        if not self.session:
            self.session = aiohttp.ClientSession()
            
        try:
            logger.info(f"Sending summary analytics data | session_id={summary_data.get('session_id')} | user_id={summary_data.get('user_id')}")
            async with self.session.post(
                url=self.summary_analytics_url,
                json=summary_data,
                headers={
                    "Content-Type": "application/json",
                    "X-API-Key": self.api_key
                },
                timeout=ANALYTICS_REQUEST_TIMEOUT
            ) as response:
                response.raise_for_status()
                logger.info(f"Summary analytics tracked successfully | session_id={summary_data.get('session_id')} | user_id={summary_data.get('user_id')}")
                return await response.json()
                
        except Exception as e:
            logger.warning(f"Failed to track summary analytics | session_id={summary_data.get('session_id')} | user_id={summary_data.get('user_id')} | error={str(e)}")
            return {"status": "error", "message": str(e)}
            
    async def close(self):
        
        if self.session:
            await self.session.close()
            self.session = None

    async def track_llm_token_costs(self, token_data: Dict[str, Any]) -> Dict[str, Any]:
        if not self.session:
            self.session = aiohttp.ClientSession()
        try:
            async with self.session.post(
                url=self.token_cost_url,
                json=token_data,
                headers={
                    "Content-Type": "application/json",
                    "X-API-Key": self.api_key
                },
                timeout=ANALYTICS_REQUEST_TIMEOUT
            ) as response:
                response.raise_for_status()
                logger.info(f"LLM token costs tracked successfully | action_ref_id={token_data.get('action_ref_id')}")
                return await response.json()
        except Exception as e:
            logger.warning(f"Failed to track LLM token costs | action_ref_id={token_data.get('action_ref_id')} | error={str(e)}")
            return {"status": "error", "message": str(e)}

    async def track_llm_traffic(self, metric_name: str, value: int) -> Dict[str, Any]:
        """
        Track LLM traffic metrics.
        
        Args:
            metric_name: One of 'requests/duration', 'requests/failed', 'requests/count'
            value: The metric value
            timestamp: Optional Unix timestamp, defaults to current time
        """
        if not self.session:
            self.session = aiohttp.ClientSession()
            
        try:
            payload = {
                "metric_name": metric_name,
                "value": value
            }
            
            timestamp = int(time.time())
            payload["timestamp"] = timestamp
                
            async with self.session.post(
                url=self.llm_traffic_url,
                json=payload,
                headers={
                    "Content-Type": "application/json",
                    "X-API-Key": self.api_key
                },
                timeout=ANALYTICS_REQUEST_TIMEOUT
            ) as response:
                response.raise_for_status()
                logger.info(f"LLM traffic metric tracked successfully | metric={metric_name} | value={value}")
                return await response.json()
                
        except Exception as e:
            logger.warning(f"Failed to track LLM traffic metric | metric={metric_name} | value={value} | error={str(e)}")
            return {"status": "error", "message": str(e)}
