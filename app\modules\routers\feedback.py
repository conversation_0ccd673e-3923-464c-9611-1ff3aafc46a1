from fastapi import APIRouter
from app.models.feedback_model import FeedbackRequest
from app.connections.analytics_handler import <PERSON>lytics<PERSON><PERSON><PERSON>
from app.core.logger import logger
from app.modules.services.session_service import SessionService
from app.modules.services.summary_db_service import SummaryDBService

router = APIRouter()
session_service = SessionService()

@router.post("/feedback")
async def submit_feedback(request: FeedbackRequest):
    logger.info(f"Received feedback request | action_ref_id={request.action_ref_id} | feedback_provided={request.feedback_provided}")
    


    feedback_data = {
        "feedback_provided": request.feedback_provided,
        "feedback_category": request.feedback_category,
        "feedback_notes": request.feedback_notes,
        "action_ref_id": request.action_ref_id
    }
    
    async with AnalyticsHandler() as analytics:
        api_result = await analytics.track_feedback(feedback_data)
    # Store in summary database
    await SummaryDBService.store_feedback(
        feedback_provided=request.feedback_provided,
        action_ref_id=request.action_ref_id,
        feedback_category=request.feedback_category,
        feedback_notes=request.feedback_notes
    )
    return api_result
