from sqlalchemy import Column, Integer, String, DateTime, Boolean
from app.connections.analytics_database import AnalyticsBase as Base
from app.core.config import TIME_SAVED_TABLE_NAME
from datetime import datetime

class TimeSaved(Base):
    __tablename__ = TIME_SAVED_TABLE_NAME
    
    id = Column(Integer, primary_key=True, index=True)
    client_id = Column(String, nullable=False)
    clinician_id = Column(String, nullable=False)
    document_version_id = Column(Integer, nullable=True)
    session_id = Column(String, nullable=True)
    start_timestamp = Column(DateTime, nullable=True)
    end_timestamp = Column(DateTime, nullable=True)
    duration = Column(Integer, nullable=True)
    procedure_code = Column(String, nullable=True)
    teams = Column(String, nullable=True)
    tool_used = Column(Boolean, nullable=False, default=False)
    teams_code = Column(String, nullable=True)
    division = Column(String, nullable=True)
    division_code = Column(String, nullable=True)
    program = Column(String, nullable=True)
    program_code = Column(String, nullable=True)
    status = Column(String, nullable=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow) 