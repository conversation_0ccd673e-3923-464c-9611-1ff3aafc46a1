from sqlalchemy import Column, Integer, String, DateTime
from app.connections.summary_database import SummaryBase as Base
from datetime import datetime

class Action(Base):
    __tablename__ = "actions"
    
    action_id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String)
    action_type = Column(String, nullable=False)
    action_ref_id = Column(String, nullable=True)
    prompt_tokens = Column(Integer)
    completion_tokens = Column(Integer)
    total_tokens = Column(Integer)
    action_timestamp = Column(DateTime, default=datetime.utcnow)
    request_timestamp = Column(DateTime)
    response_timestamp = Column(DateTime)
    total_response_time_ms = Column(Integer)
    summary = Column(String) 