from typing import Dict, Any, Optional
import aiohttp
import logging
import time
from app.core.config import LLM_REQUEST_TIMEOUT, LLM_API_BASE_URL, ANALYTICS_API_BASE_URL, ANALYTICS_API_KEY, ANALYTICS_FAILURE_LOGS_ENDPOINT, SEVERITY_CRITICAL, STATUS_UNRESOLVED, LLM_FAILURE_LOG_TYPE, FAILURE_LOG_REPOSITORY
from app.connections.analytics_handler import <PERSON>lyticsHandler
from app.utils.background_tasks import BackgroundTaskManager
import requests

class LLMRequestHandler:
    def __init__(self, base_url: str = LLM_API_BASE_URL):
        self.base_url = base_url
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    # Helper to send LLM-specific failure logs, with optional request info for context
    def _send_llm_failure(self, error_type: str, error_message: str, request_info: dict = None):
        try:
            payload = {
                "errorType": error_type,
                "errorMessage": error_message,
                "failureContext": f"{self.__class__.__module__}.{self.make_request.__name__}",
                "severity": SEVERITY_CRITICAL,
                "status": STATUS_UNRESOLVED,
                "type": LLM_FAILURE_LOG_TYPE,
                "repository": FAILURE_LOG_REPOSITORY
            }
            if request_info:
                payload["requestInfo"] = request_info
            requests.post(
                ANALYTICS_API_BASE_URL.rstrip('/') + ANALYTICS_FAILURE_LOGS_ENDPOINT,
                json=payload,
                headers={"Content-Type": "application/json", "X-API-Key": ANALYTICS_API_KEY},
                timeout=5
            )
        except Exception:
            pass

    async def _track_metrics(self, duration: int, is_error: bool = False):
        """Background task to track metrics"""
        try:
            async with AnalyticsHandler() as analytics:
                # Track request count and duration
                await analytics.track_llm_traffic("requests/count", 1)
                await analytics.track_llm_traffic("requests/duration", duration)
                if is_error:
                    await analytics.track_llm_traffic("requests/failed", 1)
        except Exception as e:
            logger = logging.getLogger("api_transactions")
            logger.error(f"Failed to track metrics: {str(e)}")

    async def make_request(
        self,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        payload: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: int = LLM_REQUEST_TIMEOUT
    ) -> Dict[str, Any]:
       
        if not self.session:
            self.session = aiohttp.ClientSession()

        full_url = url
        headers = headers or {}
        start_time = time.time()
        
        try:
            async with self.session.request(
                method=method,
                url=full_url,
                params=params,
                json=payload,
                headers=headers,
                timeout=timeout
            ) as response:
                duration = int((time.time() - start_time) * 1000)  # Convert to milliseconds
                
                # Start background task for tracking metrics
                BackgroundTaskManager.create_task(self._track_metrics(duration, response.status >= 400))
        
                if response.status >= 400:
                    logger = logging.getLogger("api_transactions")
                    logger.error(f"LLM API returned error: {response.status}, url={full_url}")
                    # Include request info in failure log
                    self._send_llm_failure(
                        f"HTTP{response.status}",
                        f"LLM API returned status code {response.status}",
                        {"method": method, "url": full_url, "params": params, "payload": payload}
                    )
                    return {
                        "status": "error",
                        "message": f"LLM API returned status code {response.status}",
                        "status_code": response.status
                    }
                return await response.json()
            
        except aiohttp.ClientError as e:
            logger = logging.getLogger("api_transactions")
            logger.error(f"Error making request to LLM API: {str(e)}, url={full_url}")
            # Include request info in failure log
            self._send_llm_failure(
                type(e).__name__,
                str(e),
                {"method": method, "url": full_url, "params": params, "payload": payload}
            )
            duration = int((time.time() - start_time) * 1000)
            BackgroundTaskManager.create_task(self._track_metrics(duration, True))
            return {
                "status": "error",
                "message": f"Error making request to LLM API: {str(e)}",
                "error_type": type(e).__name__
            }
        except Exception as e:
            logger = logging.getLogger("api_transactions")
            logger.error(f"Unexpected error while calling LLM API: {str(e)}, url={full_url}")
            # Include request info in failure log
            self._send_llm_failure(
                type(e).__name__,
                str(e),
                {"method": method, "url": full_url, "params": params, "payload": payload}
            )
            duration = int((time.time() - start_time) * 1000)
            BackgroundTaskManager.create_task(self._track_metrics(duration, True))
            return {
                "status": "error",
                "message": f"Unexpected error while calling LLM API: {str(e)}",
                "error_type": type(e).__name__
            }
