from fastapi import HTTPException
import aiohttp
from app.core.config import ANALYTICS_API_BASE_URL, ANALYTICS_FEEDBACK_ENDPOINT, ANALYTICS_API_KEY
from app.core.logger import logger

async def process_feedback(parameters: dict) -> bool:
    applied = False
    try:
        feedback_url = f"{ANALYTICS_API_BASE_URL}{ANALYTICS_FEEDBACK_ENDPOINT}/{parameters['action_ref_id']}?api_key={ANALYTICS_API_KEY}"
        async with aiohttp.ClientSession() as session:
            async with session.get(
                feedback_url, 
                headers={"Content-Type": "application/json", "X-API-Key": ANALYTICS_API_KEY}
            ) as resp:
                if resp.status == 200:
                    feedback = await resp.json()
                    feedback_category = feedback.get("feedback_category", "")
                    feedback_notes = feedback.get("feedback_notes", "")               
                    if feedback_category:
                        parameters["selected_issues"] = [issue.strip() for issue in feedback_category.split(",") if issue.strip()]
                        logger.info(f"Selected issues updated | action_ref_id={parameters['action_ref_id']} | issues={parameters['selected_issues']}")                  
                        applied = True
                    if feedback_notes:
                        existing_instr = parameters.get("user_instruction", "")
                        parameters["user_instruction"] = (existing_instr + " " + feedback_notes).strip() if existing_instr else feedback_notes
                        logger.info(f"User instruction updated | action_ref_id={parameters['action_ref_id']} | instruction={parameters['user_instruction']}")                  
                        applied = True
                    logger.info(f"Feedback applied successfully | action_ref_id={parameters['action_ref_id']}")
                else:
                    logger.warning(f"Failed to fetch feedback | action_ref_id={parameters['action_ref_id']} | status={resp.status}")
    except Exception as e:
        logger.warning(f"Error processing feedback | action_ref_id={parameters['action_ref_id']} | error={str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing feedback: {str(e)}")
    return applied