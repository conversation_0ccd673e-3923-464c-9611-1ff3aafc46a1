<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>Hybrid Encryption Demo</title>
  <style>
    body {
      font-family: Arial;
      max-width: 800px;
      margin: auto;
      padding: 20px;
    }

    textarea {
      width: 100%;
      height: 220px;
      font-family: monospace;
      padding: 10px;
      margin-bottom: 10px;
    }

    button {
      padding: 10px 20px;
      background: #007bff;
      color: white;
      border: none;
      cursor: pointer;
    }

    .output {
      white-space: pre-wrap;
      background: #f0f0f0;
      padding: 15px;
      margin-top: 20px;
      border-radius: 5px;
    }
  </style>
</head>

<body>
  <h2>🔐 Hybrid Encryption (RSA + AES-GCM)</h2>

  <textarea id="jsonInput">
{
  "action_type": "generate",
  "client_id": "19701",
  "documentversion_id": 16844488,
  "parameters": {
    "pronoun": "They",
    "case_worker_name": "staff",
    "procedure_code": "IATP LOCUS",
    "needs": null,
    "objectives": null,
    "interventions": null,
    "mode_of_delivery": "Face-to-face",
    "member_presentation": "Withdrawn Behavior. Anxious Mood. Neat Appearance. Normal Mood",
    "response_to_interventions": "Engaged",
    "progress_towards_objectives": "Improving",
    "member_quotes": "",
    "service_duration": "10",
    "member_response": "This a sample member response",
    "member_present": "True",
    "notes": "",
    "user_instruction": "",
    "selected_issues": [],
    "action_ref_id": null
  }
}
  </textarea>

  <button onclick="sendEncrypted()">Encrypt & Send</button>

  <div id="output" class="output"></div>

  <script>
    let publicKey;

    async function fetchPublicKey() {
      const res = await fetch("http://localhost:8080/api/v1/public-key", {
        method: "GET",
        headers: { "Content-Type": "application/json", "X-API-Key": "alpha_tool_1rtxWMBD0B4urvXmJCKR3h9UE55zt5K5vOfcnZt3tHf9Oc3kkKzO5RT0RSJhwFps" },
      });
      const { public_key_pem } = await res.json();
      return await window.crypto.subtle.importKey(
        "spki",
        pemToArrayBuffer(public_key_pem),
        { name: "RSA-OAEP", hash: "SHA-256" },
        false,
        ["encrypt"]
      );
    }

    function pemToArrayBuffer(pem) {
      const b64 = pem.replace(/-----.*?-----/g, "").replace(/\s+/g, "");
      const binary = atob(b64);
      const bytes = new Uint8Array(binary.length);
      for (let i = 0; i < binary.length; i++) bytes[i] = binary.charCodeAt(i);
      return bytes.buffer;
    }

    function arrayBufferToBase64(buffer) {
      return btoa(String.fromCharCode(...new Uint8Array(buffer)));
    }

    function base64ToUint8Array(base64) {
      return Uint8Array.from(atob(base64), c => c.charCodeAt(0));
    }

    async function sendEncrypted() {
      const outputDiv = document.getElementById("output");
      outputDiv.textContent = "🔐 Starting encryption flow...";

      try {
        // Step 1: Parse JSON
        const input = document.getElementById("jsonInput").value;
        let json = null;
        try {
          json = JSON.parse(input);
        } catch (e) {
          outputDiv.textContent = "❌ Invalid JSON input.";
          return;
        }

        const plaintext = JSON.stringify(json);
        console.log("✅ JSON parsed:", plaintext);

        // Step 2: Generate AES key + nonce
        const aesKeyBytes = crypto.getRandomValues(new Uint8Array(32));
        const nonce = crypto.getRandomValues(new Uint8Array(12));

        const aesKey = await crypto.subtle.importKey(
          "raw", aesKeyBytes, { name: "AES-GCM" }, false, ["encrypt", "decrypt"]
        );

        // Step 3: Encrypt JSON
        const encoded = new TextEncoder().encode(plaintext);
        const encryptedBuffer = await crypto.subtle.encrypt(
          { name: "AES-GCM", iv: nonce },
          aesKey,
          encoded
        );

        console.log("✅ AES encryption complete");

        // Step 4: Fetch public key if not already
        if (!publicKey) {
          publicKey = await fetchPublicKey();
          console.log("✅ Public key fetched and imported");
        }

        // Step 5: Encrypt AES key with RSA public key
        const encryptedAesKey = await crypto.subtle.encrypt(
          { name: "RSA-OAEP" },
          publicKey,
          aesKeyBytes
        );

        console.log("✅ AES key encrypted with RSA");

        // Step 6: Construct payload
        const payload = {
          encrypted_data: arrayBufferToBase64(encryptedBuffer),
          encrypted_key: arrayBufferToBase64(encryptedAesKey),
          nonce: arrayBufferToBase64(nonce)
        };

        console.log("📤 Sending payload to backend");

        // Step 7: Send to backend
        const res = await fetch("http://localhost:8080/api/v1/tool-action", {
          method: "POST",
          headers: { "Content-Type": "application/json", "X-API-Key": "alpha_tool_1rtxWMBD0B4urvXmJCKR3h9UE55zt5K5vOfcnZt3tHf9Oc3kkKzO5RT0RSJhwFps" },
          body: JSON.stringify(payload)
        });

        const result = await res.json();

        if (!res.ok) {
          outputDiv.textContent = `❌ Backend Error:\n${result.detail || "Unknown error"}`;
          return;
        }

        console.log("✅ Received encrypted response from backend");

        // Step 8: Decrypt backend response
        const encryptedResp = base64ToUint8Array(result.encrypted_data);
        const respNonce = base64ToUint8Array(result.nonce);

        const ciphertext = encryptedResp.slice(0, -16);
        const tag = encryptedResp.slice(-16);
        const fullResponse = new Uint8Array([...ciphertext, ...tag]);

        const decryptedBuffer = await crypto.subtle.decrypt(
          { name: "AES-GCM", iv: respNonce },
          aesKey,
          fullResponse
        );

        const responseText = new TextDecoder().decode(decryptedBuffer);
        outputDiv.textContent = `✅ Server Response:\n${responseText}`;
        console.log("✅ Response decrypted");

      } catch (err) {
        console.error("❌ Encryption failed:", err);
        document.getElementById("output").textContent = "❌ Encryption or request failed:\n" + err.message;
      }
    }
  </script>

</body>

</html>