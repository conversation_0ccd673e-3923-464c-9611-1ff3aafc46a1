from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from app.connections.summary_database import SummaryBase as Base

class SummaryAnalytics(Base):
    __tablename__ = "summary_analytics"
    
    summary_id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String)
    session_id = Column(String)
    clinician_type = Column(String)
    procedure_code = Column(String)
    auto_completion_used = Column(Boolean, default=False)
    summary_finalized_timestamp = Column(DateTime)
    summary_finalized = Column(Boolean, default=False)
    start_timestamp = Column(DateTime)
    end_timestamp = Column(DateTime)
    time_consumed_secs = Column(Integer)
    summary = Column(Text, nullable=True) 