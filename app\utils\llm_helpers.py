from app.core.config import (
    LLM_API_BASE_URL,
    LLM_API_GENERATE_ENDPOINT,
    LLM_API_CLEANUP_ENDPOINT,
    LLM_API_FINETUNE_ENDPOINT
)

def get_action_url(action_type: str) -> str:
    """
    Construct the full URL for the given action type. If the configured endpoint is a full URL,
    return it directly; otherwise, join it to the base URL.
    """
    base_url = LLM_API_BASE_URL.rstrip("/")
    endpoint_map = {
        'generate': LLM_API_GENERATE_ENDPOINT,
        'cleanup': LLM_API_CLEANUP_ENDPOINT,
        'finetune': LLM_API_FINETUNE_ENDPOINT,
        'regenerate': LLM_API_GENERATE_ENDPOINT
    }
    endpoint = endpoint_map.get(action_type.lower())
    if not endpoint:
        raise ValueError(f"Invalid action type: {action_type}")
    # If endpoint is already a full URL, return it directly
    if endpoint.startswith("http://") or endpoint.startswith("https://"):
        return endpoint
    # Ensure endpoint has leading slash
    if not endpoint.startswith("/"):
        endpoint = "/" + endpoint
    return f"{base_url}{endpoint}"