import logging
from urllib.parse import quote_plus
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import NullPool
from app.core.config import SUMMARY_DB_SERVER, SUMMARY_DB_DATABASE, SUMMARY_DB_USERNAME, SUMMARY_DB_PASSWORD, SUMMARY_DB_DRIVER
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

# Log creation of async engine
logger.info("Summary async engine configured.")

SummaryBase = declarative_base()

def generate_summary_database_url() -> str:
    server_parts = SUMMARY_DB_SERVER.split(',')
    server = server_parts[0]
    port = server_parts[1] if len(server_parts) > 1 else '1433'
    
    quoted_password = quote_plus(SUMMARY_DB_PASSWORD)
    quoted_driver = quote_plus(SUMMARY_DB_DRIVER)
    
    return f"mssql+aioodbc://{SUMMARY_DB_USERNAME}:{quoted_password}@{server}:{port}/{SUMMARY_DB_DATABASE}?driver={quoted_driver}&TrustServerCertificate=yes&Encrypt=yes&ConnectionTimeout=30&LoginTimeout=30"

SUMMARY_DATABASE_URL = generate_summary_database_url()

summary_engine = create_async_engine(
    SUMMARY_DATABASE_URL,
    echo=False,
    poolclass=NullPool,
    connect_args={
        "timeout": 30,
        "autocommit": False
    }
)

summary_async_session = sessionmaker(
    summary_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

@asynccontextmanager
async def get_summary_db() -> AsyncGenerator[AsyncSession, None]:
    session = summary_async_session()
    logger.info("Opening summary DB session")
    try:
        yield session
        await session.commit()
        logger.info("Committed summary DB session")
    except Exception as e:
        await session.rollback()
        logger.exception("Exception occurred in get_summary_db: %s", e)
        raise
    finally:
        await session.close()
        logger.info("Closed summary DB session")

async def init_summary_db():
    try:
        pass
        
        async with summary_engine.begin() as conn:
            await conn.run_sync(SummaryBase.metadata.create_all)
            logger.info("Summary database tables created successfully")
    except Exception as e:
        logger.exception("Exception occurred during summary database initialization: %s", e)
        raise

async def close_summary_db_connection():
    try:
        await summary_engine.dispose()
    except Exception as e:
        logger.exception("Exception occurred while closing the summary database connection: %s", e)
        raise 