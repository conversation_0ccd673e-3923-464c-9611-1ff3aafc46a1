from fastapi import Request, HTTPException
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from app.core.config import API_TITLE, API_KEY, ENVIRONMENT, DOCS_OAUTH2_REDIRECT_URL, SWAGGER_JS_URL, SWAGGER_CSS_URL, REDOC_JS_URL
from app.core.logger import logger

def setup_docs_routes(app):
    @app.get("/", tags=["Base"])
    async def read_root(request: Request):
        logger.info("Root endpoint accessed")
        return f"{API_TITLE} - {ENVIRONMENT} is running."

    
    @app.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html(request: Request):
       
        api_key = request.query_params.get('api_key', '')
        if api_key != API_KEY:
            logger.warning(f"Invalid API key attempt for docs | api_key={api_key[:8]}...")
            raise HTTPException(status_code=403, detail="Invalid API Key")
            
        logger.info("Swagger UI accessed")
        return get_swagger_ui_html(
            openapi_url=f"/openapi.json?api_key={api_key}",
            title=f"{API_TITLE} - Swagger UI",
            oauth2_redirect_url=DOCS_OAUTH2_REDIRECT_URL,
            swagger_js_url=SWAGGER_JS_URL,
            swagger_css_url=SWAGGER_CSS_URL,
        )

    @app.get("/redoc", include_in_schema=False)
    async def redoc_html(request: Request):
        
        api_key = request.query_params.get('api_key', '')
        if api_key != API_KEY:
            logger.warning(f"Invalid API key attempt for redoc | api_key={api_key[:8]}...")
            raise HTTPException(status_code=403, detail="Invalid API Key")
            
        logger.info("ReDoc UI accessed")
        return get_redoc_html(
            openapi_url=f"/openapi.json?api_key={api_key}",
            title=f"{API_TITLE} - ReDoc",
            redoc_js_url=REDOC_JS_URL,
        )
    
    
    @app.get("/openapi.json", include_in_schema=False)
    async def get_open_api_endpoint(request: Request, api_key: str = None):
        if api_key != API_KEY:
            logger.warning(f"Invalid API key attempt for openapi | api_key={api_key[:8] if api_key else 'None'}...")
            raise HTTPException(status_code=403, detail="Invalid API Key")
        logger.info("OpenAPI schema accessed")
        return app.openapi() 