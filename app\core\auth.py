from fastapi.security import <PERSON><PERSON>eyHeader
from fastapi import HTTPException, Depends
from app.core.config import API_KEY
from app.core.logger import logger
api_key_header = APIKeyHeader(name="X-API-Key")

async def verify_api_key(api_key: str = Depends(api_key_header)):
    if api_key != API_KEY:
        logger.warning(f"Invalid API key attempt | api_key={api_key}")
        raise HTTPException(
            status_code=403,
            detail="Invalid API Key"
        )
    return api_key